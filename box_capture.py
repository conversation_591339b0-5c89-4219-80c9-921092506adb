import json

import numpy as np
import pandas as pd
from datetime import datetime
import time


def dataload():
    # 读取 JSON 文件
    with open('BTCUSDT_4h.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 转换为 DataFrame
    columns = [
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_volume', 'trades',
        'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
    ]
    df = pd.DataFrame(data, columns=columns)

    # 将时间戳转换为可读时间
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')

    return df


def compress_consecutive(arr):
    arr = np.asarray(arr)
    if len(arr) == 0:
        return arr
    # 找出变化的位置
    change_points = np.where(np.diff(arr) != 0)[0] + 1
    # 第一个元素一定要保留
    compressed = np.concatenate(([arr[0]], arr[change_points]))
    # 删除为 0 的元素
    compressed = compressed[compressed != 0]
    return compressed


def evaluate(interval):
    split_rate = 10
    # 第一步：创建一个形状相同的新数组
    labels = np.zeros_like(interval)

    # 第二步：计算分位数
    lower_threshold = np.percentile(interval, split_rate)  # 最小10%
    upper_threshold = np.percentile(interval, 100 - split_rate)  # 最大10%

    # 第三步：根据阈值进行标记
    labels[interval > upper_threshold] = 1
    labels[interval < lower_threshold] = -1
    # 其余保持为0
    pass

    # 一次转换记为一分
    compressed = compress_consecutive(labels)

    return len(compressed) - 1


def box_score(close):
    n = len(close)
    # 设置最小长度
    min_length = 20
    # 记录分数
    scores = np.zeros(shape=(n, n))
    # 枚举箱体边界
    for left in range(n):
        if left % 100 == 0:
            print(f'当前 left = {left}')
        for right in range(left + min_length, n):
            scores[left][right] = evaluate(close[left:right + 1])

    # 可选：把下三角设为 NaN 更清晰
    scores = np.triu(scores, k=1)  # 只保留上三角（不含对角线），下三角为0
    scores[scores == 0] = np.nan  # 用 NaN 替代未计算部分（可选）

    # 保存到Excel
    df = pd.DataFrame(scores)
    df.to_excel('scores.xlsx', index=False, header=False)


if __name__ == '__main__':
    start_time = time.time()
    data = dataload()
    close = data['close'].astype(float).to_numpy()
    box_score(close)
    end_time = time.time()
    print(f"程序运行时间：{end_time - start_time}")