import json
import math
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import accuracy_score
import matplotlib.pyplot as plt

# 超参数配置
config = {
    "d": 80,
    "batch_size": 64,
    "d_model": 128,
    "nhead": 8,
    "num_layers": 6,
    "dim_feedforward": 512,
    "dropout": 0.1,
    "lr": 1e-4,
    "num_epochs": 200,
    "confidence_threshold": 0.7,
    "device": torch.device("cuda" if torch.cuda.is_available() else "cpu")
}

# 数据预处理函数（全局归一化+序列标签生成）
def compute_normalization_params(file_path):
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # 收集所有价格和成交量数据
    price_data = []
    volume_data = []
    for tp in data:
        price_data.append([float(tp[1]), float(tp[2]), float(tp[3]), float(tp[4])])
        volume_data.append(float(tp[5]))
    
    price_data = np.array(price_data)
    volume_data = np.array(volume_data)
    
    price_mean = np.mean(price_data, axis=0)
    price_std = np.std(price_data, axis=0)
    price_std = np.where(price_std == 0, 1e-8, price_std)
    
    volume_mean = np.mean(volume_data)
    volume_std = np.std(volume_data)
    volume_std = volume_std if volume_std != 0 else 1e-8
    
    return {
        'price_mean': price_mean,
        'price_std': price_std,
        'volume_mean': volume_mean,
        'volume_std': volume_std
    }

def load_and_process_data(file_path, window_size, norm_params):
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    samples = []
    labels_cls = []
    labels_reg = []
    
    price_mean = norm_params['price_mean']
    price_std = norm_params['price_std']
    volume_mean = norm_params['volume_mean']
    volume_std = norm_params['volume_std']
    
    for i in range(len(data) - window_size - 1):
        window = data[i:i+window_size+1]  # 包含目标步
        
        # 输入特征处理
        input_features = []
        for j in range(window_size):
            tp = window[j]
            price_features = [float(tp[1]), float(tp[2]), float(tp[3]), float(tp[4])]
            volume = float(tp[5])
            normalized_price = (price_features - price_mean) / price_std
            normalized_volume = (volume - volume_mean) / volume_std
            input_features.append(normalized_price + [normalized_volume])
        
        # 标签处理（每个时间步预测下一步）
        cls_labels = []
        reg_labels = []
        for j in range(window_size):
            current_close = float(window[j][4])
            future_close = float(window[j+1][4])
            label_cls = 1 if future_close > current_close else 0
            label_reg = (future_close - current_close) / price_std[3]
            cls_labels.append(label_cls)
            reg_labels.append(label_reg)
        
        samples.append(input_features)
        labels_cls.append(cls_labels)
        labels_reg.append(reg_labels)
    
    return np.array(samples), np.array(labels_cls), np.array(labels_reg)

class BTCDataset(Dataset):
    def __init__(self, samples, labels_cls, labels_reg):
        self.samples = torch.FloatTensor(samples)
        self.labels_cls = torch.LongTensor(labels_cls)
        self.labels_reg = torch.FloatTensor(labels_reg)
        
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        return self.samples[idx], self.labels_cls[idx], self.labels_reg[idx]

class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(max_len, 1, d_model)
        pe[:, 0, 0::2] = torch.sin(position * div_term)
        pe[:, 0, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe)

    def forward(self, x):
        x = x + self.pe[:x.size(0)]
        return self.dropout(x)

class GPT2PricePredictor(nn.Module):
    def __init__(self, config):
        super().__init__()
        input_features = 5
        self.embedding = nn.Linear(input_features, config["d_model"])
        self.pos_encoder = PositionalEncoding(config["d_model"], config["dropout"])
        
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=config["d_model"],
            nhead=config["nhead"],
            dim_feedforward=config["dim_feedforward"],
            dropout=config["dropout"],
            activation='gelu'
        )
        self.transformer = nn.TransformerDecoder(decoder_layer, config["num_layers"])
        
        # 每个时间步的双任务预测头
        self.classifier = nn.Linear(config["d_model"], 2)
        self.regressor = nn.Linear(config["d_model"], 1)
        
        self._init_weights()

    def _init_weights(self):
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, x):
        # x形状: (batch_size, seq_len, 5)
        batch_size, seq_len, _ = x.size()
        x = self.embedding(x)  # (batch_size, seq_len, d_model)
        x = x.permute(1, 0, 2)  # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        
        tgt_mask = nn.Transformer.generate_square_subsequent_mask(seq_len).to(x.device)
        output = self.transformer(tgt=x, memory=x, tgt_mask=tgt_mask)  # (seq_len, batch_size, d_model)
        
        # 全序列预测
        cls_logits = self.classifier(output)  # (seq_len, batch_size, 2)
        reg_values = self.regressor(output).squeeze(-1)  # (seq_len, batch_size)
        
        return cls_logits.permute(1,0,2), reg_values.permute(1,0)  # (batch, seq, 2), (batch, seq)

def analyze_confidence(probs, labels, thresholds):
    probs_max = np.max(probs, axis=1)
    results = []
    for th in thresholds:
        mask = probs_max >= th
        if not mask.any():
            results.append((th, 0, 0))
            continue
            
        acc = accuracy_score(labels[mask], np.argmax(probs, axis=1)[mask])
        coverage = mask.mean()
        results.append((th, acc, coverage))
    return results

def train_model():
    # 计算全局归一化参数
    dataset_base = './data/BTCUSDT_15m_dataset'
    norm_params = compute_normalization_params(f'{dataset_base}/train.json')
    
    # 加载数据
    X_train, y_train_cls, y_train_reg = load_and_process_data(
        f'{dataset_base}/train.json', config["d"], norm_params)
    X_val, y_val_cls, y_val_reg = load_and_process_data(
        f'{dataset_base}/val.json', config["d"], norm_params)
    
    # 创建DataLoader
    train_loader = DataLoader(BTCDataset(X_train, y_train_cls, y_train_reg), 
                            batch_size=config["batch_size"], shuffle=True)
    val_loader = DataLoader(BTCDataset(X_val, y_val_cls, y_val_reg), 
                          batch_size=config["batch_size"], shuffle=False)
    
    # 初始化模型
    model = GPT2PricePredictor(config).to(config["device"])
    criterion_cls = nn.CrossEntropyLoss()
    criterion_reg = nn.MSELoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=config["lr"])
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'max', patience=5, factor=0.5)
    
    best_val_acc = 0
    threshold_history = []

    for epoch in range(config["num_epochs"]):
        model.train()
        total_loss = 0
        
        # 训练阶段（全序列预测）
        for inputs, labels_cls, labels_reg in train_loader:
            inputs = inputs.to(config["device"])
            labels_cls = labels_cls.long().to(config["device"])  # (B, L)
            labels_reg = labels_reg.float().to(config["device"])  # (B, L)
            
            optimizer.zero_grad()
            cls_out, reg_out = model(inputs)  # (B, L, 2), (B, L)
            
            # 全序列损失计算
            loss_cls = criterion_cls(cls_out.view(-1,2), labels_cls.view(-1))
            loss_reg = criterion_reg(reg_out.view(-1), labels_reg.view(-1))
            loss = loss_cls + loss_reg
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            total_loss += loss.item()
        
        # 验证阶段（仅最后一个时间步）
        model.eval()
        all_probs = []
        all_labels = []
        val_loss_reg = 0
        
        with torch.no_grad():
            for inputs, labels_cls, labels_reg in val_loader:
                inputs = inputs.to(config["device"])
                labels_cls = labels_cls[:,-1].long().to(config["device"])  # 取最后一个时间步
                labels_reg = labels_reg[:,-1].float().to(config["device"])
                
                cls_out, reg_out = model(inputs)
                last_cls = cls_out[:,-1,:]  # (B,2)
                probs = torch.softmax(last_cls, dim=1)
                
                all_probs.append(probs.cpu().numpy())
                all_labels.append(labels_cls.cpu().numpy())
                val_loss_reg += criterion_reg(reg_out[:,-1], labels_reg).item()
        
        probs = np.concatenate(all_probs)
        labels = np.concatenate(all_labels)
        analysis_results = analyze_confidence(probs, labels, np.arange(0.5, 0.96, 0.05))
        
        best_th = config["confidence_threshold"]
        best_acc = 0
        print(f"\nEpoch {epoch+1} Threshold Analysis:")
        for th, acc, coverage in analysis_results:
            print(f"  Thresh: {th:.2f} | Acc: {acc:.4f} | Coverage: {coverage:.4f}")
            if acc > best_acc and coverage > 0.05:
                best_acc = acc
                best_th = th
        
        threshold_history.append(best_th)
        mask = np.max(probs, axis=1) >= best_th
        final_acc = accuracy_score(labels[mask], np.argmax(probs[mask], axis=1)) if mask.any() else 0
        final_coverage = mask.mean()
        
        train_loss = total_loss / len(train_loader)
        val_loss_reg = val_loss_reg / len(val_loader)
        scheduler.step(final_acc)
        
        print(f"\nEpoch {epoch+1} Summary:")
        print(f"Train Loss: {train_loss:.4f} | Val Reg Loss: {val_loss_reg:.4f}")
        print(f"Best Threshold: {best_th:.2f} | Filtered Acc: {final_acc:.4f} | Coverage: {final_coverage:.4f}")
        print("-"*60)
        
        if final_acc > best_val_acc:
            best_val_acc = final_acc
            torch.save({
                'model_state_dict': model.state_dict(),
                'threshold_history': threshold_history,
                'best_threshold': best_th
            }, 'best_gpt_model.pth')
    
    print("训练完成")
    return model

def predict_with_confidence(model, sample, threshold=None):
    model.eval()
    with torch.no_grad():
        sample_tensor = torch.FloatTensor(sample).unsqueeze(0).to(config["device"])
        cls_out, reg_out = model(sample_tensor)
        
        probs = torch.softmax(cls_out, dim=1)
        confidence, pred = torch.max(probs, dim=1)
        
        if threshold is None:
            checkpoint = torch.load('best_gpt_model.pth')
            threshold = checkpoint['best_threshold']
        
        if confidence.item() >= threshold:
            return {
                'prediction': pred.item(),
                'confidence': confidence.item(),
                'reg_value': reg_out.item(),
                'threshold': threshold
            }
        else:
            return {
                'prediction': None,
                'confidence': confidence.item(),
                'reg_value': reg_out.item(),
                'threshold': threshold
            }

if __name__ == "__main__":
    trained_model = train_model()