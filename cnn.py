import json
import math
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import accuracy_score
import matplotlib.pyplot as plt

# 超参数配置
config = {
    "d": 80,
    "batch_size": 64,
    "d_model": 64,
    "nhead": 4,
    "num_layers": 3,
    "dim_feedforward": 256,
    "dropout": 0.1,
    "lr": 1e-4,
    "num_epochs": 2000,
    "confidence_threshold": 0.7,  # 默认置信度阈值
    "device": torch.device("cuda" if torch.cuda.is_available() else "cpu")
}

# 数据预处理（保持不变）
def load_and_process_data(file_path, window_size):
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    samples = []
    labels_cls = []
    labels_reg = []
    price_feature_indices = [1, 2, 3, 4]
    
    for i in range(len(data) - window_size - 1):
        window = data[i:i+window_size]
        
        price_features = []
        for tp in window:
            price_features.append([float(tp[idx]) for idx in price_feature_indices])
        price_features = np.array(price_features)
        
        price_mean = price_features.mean()
        price_std = price_features.std()
        price_std = price_std if price_std != 0 else 1e-8
        normalized_price = (price_features - price_mean) / price_std
        
        current_close = float(data[i+window_size-1][4])
        future_close = float(data[i+window_size][4])
        label_cls = 1 if future_close > current_close else 0

        samples.append(normalized_price)
        labels_cls.append(label_cls)
        labels_reg.append((future_close - current_close) / price_std)
    
    return np.array(samples), np.array(labels_cls), np.array(labels_reg)

# 数据集类（保持不变）
class BTCDataset(Dataset):
    def __init__(self, samples, labels_cls, labels_reg):
        self.samples = torch.FloatTensor(samples)
        self.labels_cls = torch.LongTensor(labels_cls)
        self.labels_reg = torch.FloatTensor(labels_reg)
        
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        return self.samples[idx], self.labels_cls[idx], self.labels_reg[idx]

# 模型定义（保持不变）
class CNNPricePredictor(nn.Module):
    def __init__(self, config):
        super(CNNPricePredictor, self).__init__()
        self.config = config
        self.conv1 = nn.Conv1d(in_channels=4, out_channels=32, kernel_size=3)
        self.conv2 = nn.Conv1d(in_channels=32, out_channels=64, kernel_size=3)
        self.conv3 = nn.Conv1d(in_channels=64, out_channels=128, kernel_size=3)
        self.fc1 = nn.Linear(128, 64)
        self.fc2 = nn.Linear(64, 2)
        self.fc3 = nn.Linear(64, 1)
    
    def forward(self, x):
        x = x.permute(0, 2, 1)
        x = self.conv1(x)
        x = nn.functional.relu(x)
        x = self.conv2(x)
        x = nn.functional.relu(x)
        x = self.conv3(x)
        x = nn.functional.relu(x)
        x = nn.functional.max_pool1d(x, x.size(2))
        x = x.view(-1, 128)
        x = self.fc1(x)
        x = nn.functional.relu(x)
        cls_out = self.fc2(x)
        reg_out = self.fc3(x)
        return cls_out, reg_out

# 新增置信度分析函数
def analyze_confidence(probs, labels, thresholds):
    probs_max = np.max(probs, axis=1)
    results = []
    for th in thresholds:
        mask = probs_max >= th
        if not mask.any():
            results.append((th, 0, 0))
            continue
            
        acc = accuracy_score(labels[mask], np.argmax(probs, axis=1)[mask])
        coverage = sum(mask) / len(mask)
        results.append((th, acc, coverage))
    return results

# 修改后的训练流程
def train_model():
    # 加载数据
    dataset_base = './data/BTCUSDT_15m_dataset'
    X_train, y_train_cls, y_train_reg = load_and_process_data(f'{dataset_base}/train.json', config["d"])
    X_val, y_val_cls, y_val_reg = load_and_process_data(f'{dataset_base}/val.json', config["d"])
    
    # 创建DataLoader
    train_loader = DataLoader(BTCDataset(X_train, y_train_cls, y_train_reg), 
                            batch_size=config["batch_size"], shuffle=True)
    val_loader = DataLoader(BTCDataset(X_val, y_val_cls, y_val_reg), 
                          batch_size=config["batch_size"], shuffle=False)
    
    # 初始化模型
    model = CNNPricePredictor(config).to(config["device"])
    criterion_cls = nn.CrossEntropyLoss()
    criterion_reg = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=config["lr"])
    
    best_val_acc = 0
    best_model = None
    threshold_history = []

    for epoch in range(config["num_epochs"]):
        model.train()
        total_loss = 0
        
        # 训练阶段
        for inputs, labels_cls, labels_reg in train_loader:
            inputs = inputs.to(config["device"])
            labels_cls = labels_cls.to(config["device"])
            labels_reg = labels_reg.to(config["device"])
            
            optimizer.zero_grad()
            cls_out, reg_out = model(inputs)
            loss_cls = criterion_cls(cls_out, labels_cls)
            loss_reg = criterion_reg(reg_out.squeeze(), labels_reg)
            loss = loss_cls + loss_reg
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        # 验证阶段
        model.eval()
        all_probs = []
        all_labels = []
        val_loss_reg = 0
        
        with torch.no_grad():
            for inputs, labels_cls, labels_reg in val_loader:
                inputs = inputs.to(config["device"])
                labels_cls = labels_cls.to(config["device"])
                labels_reg = labels_reg.to(config["device"])
                
                cls_out, reg_out = model(inputs)
                probs = torch.softmax(cls_out, dim=1) # BxC
                
                all_probs.append(probs.cpu().numpy())
                all_labels.append(labels_cls.cpu().numpy())
                val_loss_reg += criterion_reg(reg_out.squeeze(), labels_reg).item()
        
        # 合并结果
        probs = np.concatenate(all_probs) # Nx2
        labels = np.concatenate(all_labels) # N
        max_probs = np.max(probs, axis=1)
        
        # 阈值分析
        thresholds = np.arange(0.5, 0.96, 0.05)
        analysis_results = analyze_confidence(probs, labels, thresholds)
        
        # 选择最佳阈值
        best_th = config["confidence_threshold"]
        best_acc = 0
        print(f"\nEpoch {epoch+1} Threshold Analysis:")
        for th, acc, coverage in analysis_results:
            print(f"  Thresh: {th:.2f} | Acc: {acc:.4f} | Coverage: {coverage:.4f}")
            if acc > best_acc and coverage > 0.03:  # 保证至少10%的覆盖率
                best_acc = acc
                best_th = th
        
        # 保存最佳阈值
        threshold_history.append(best_th)
        
        # 计算指标
        mask = max_probs >= best_th
        final_acc = accuracy_score(labels[mask], np.argmax(probs[mask], axis=1)) if sum(mask) > 0 else 0
        final_coverage = sum(mask) / len(mask)
        train_loss = total_loss / len(train_loader)
        val_loss_reg = val_loss_reg / len(val_loader)

        print(f"\nEpoch {epoch+1} Summary:")
        print(f"Train Loss: {train_loss:.4f} | Val Reg Loss: {val_loss_reg:.4f}")
        print(f"Best Threshold: {best_th:.2f} | Filtered Acc: {final_acc:.4f} | Coverage: {final_coverage:.4f}")
        print("-"*60)
        
        # 保存最佳模型
        if final_acc > best_val_acc:
            best_val_acc = final_acc
            best_model = model.state_dict()
    
    # 保存最终模型和阈值
    torch.save({
        'model_state_dict': best_model,
        'threshold_history': threshold_history,
        'best_threshold': np.mean(threshold_history[-10:])  # 取最后10个epoch的平均
    }, 'best_model.pth')
    
    print("训练完成")
    return model

# 预测函数
def predict_with_confidence(model, sample, threshold=None):
    model.eval()
    with torch.no_grad():
        sample_tensor = torch.FloatTensor(sample).unsqueeze(0).to(config["device"])
        cls_out, reg_out = model(sample_tensor)
        
        probs = torch.softmax(cls_out, dim=1)
        confidence, pred = torch.max(probs, dim=1)
        
        if threshold is None:  # 使用训练确定的平均阈值
            checkpoint = torch.load('best_model.pth')
            threshold = checkpoint['best_threshold']
        
        if confidence.item() >= threshold:
            return {
                'prediction': pred.item(),
                'confidence': confidence.item(),
                'reg_value': reg_out.item(),
                'threshold': threshold
            }
        else:
            return {
                'prediction': None,
                'confidence': confidence.item(),
                'reg_value': reg_out.item(),
                'threshold': threshold
            }

if __name__ == "__main__":
    trained_model = train_model()
    
    # 示例预测
    # sample = np.random.randn(config["d"], 4)  # 替换为实际数据
    # result = predict_with_confidence(trained_model, sample)
    
    # print("\n预测结果示例:")
    # print(f"置信度: {result['confidence']:.4f} (阈值: {result['threshold']:.2f})")
    # if result['prediction'] is not None:
    #     print(f"交易信号: {'买入' if result['prediction'] == 1 else '卖出'}")
    #     print(f"预测价格变动: {result['reg_value']:.4f}σ")
    # else:
    #     print("未达到置信度阈值，跳过交易")