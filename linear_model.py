import json
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression

from matplotlib import pyplot as plt
from tqdm import tqdm

# [
#   [
#     1499040000000,      // 开盘时间
#     "0.01634790",       // 开盘价
#     "0.80000000",       // 最高价
#     "0.01575800",       // 最低价
#     "0.01577100",       // 收盘价(当前K线未结束的即为最新价)
#     "148976.11427815",  // 成交量
#     1499644799999,      // 收盘时间
#     "2434.19055334",    // 成交额
#     308,                // 成交笔数
#     "1756.87402397",    // 主动买入成交量
#     "28.46694368",      // 主动买入成交额
#     "17928899.62484339" // 请忽略该参数
#   ]
# ]

dataset_base = './data/BTCUSDT_15m_dataset'

def load_dataset(fname: str, d: int = 10):
    with open(fname, "r") as f:
        data = json.load(f)

    columns = [
        "open_time", "open", "high", "low", "close", 
        "volume", "close_time", "quote_volume", 
        "trades", "taker_buy_base_volume", 
        "taker_buy_quote_volume", "ignore"
    ]

    df = pd.DataFrame(data, columns=columns)

    for col in ["open", "high", "low", "close", "volume", "quote_volume",
                "taker_buy_base_volume", "taker_buy_quote_volume"]:
        df[col] = df[col].astype(float)

    close_prices = df["close"].values
    n = len(close_prices)

    X = []
    y = []

    for i in range(d, n - 2):
        feature = close_prices[i-d : i] # i-d ~ i-1
        X.append(feature)

        label = close_prices[i+2]
        y.append(label)

    X = np.array(X)
    y = np.array(y)
    row_means = np.mean(X, axis=1, keepdims=True)
    row_stds = np.std(X, axis=1, keepdims=True)
    X = (X - row_means) / (row_stds + 1e-8)
    y = (y - row_means.squeeze()) / (row_stds.squeeze() + 1e-8)
    means = row_means.squeeze()
    stds = row_stds.squeeze()

    current_prices = X[:, -1]
    return X, y, current_prices, means, stds

def main(num_past_samples: int):
    X_train, y_train, current_close_train, means_train, stds_train = load_dataset(f'{dataset_base}/train.json', num_past_samples)
    X_test, y_test, current_close_test, means_test, stds_test = load_dataset(f'{dataset_base}/val.json', num_past_samples)

    model = LinearRegression()
    model.fit(X_train, y_train)

    y_pred = model.predict(X_test)

    mae = np.mean(np.abs(y_pred - y_test))
    mre = np.mean(np.abs(y_pred - y_test) / current_close_test)

    # Accuracy
    real_change = y_test - current_close_test
    pred_change = y_pred - current_close_test

    correct_direction = (real_change * pred_change) > 0  # True/False
    up_down_accuracy = np.mean(correct_direction)

    print(f"Results for d={X_train.shape[1]}")
    print(f"MAE: {mae:.6f}")
    print(f"MRE (relative to t close): {mre:.6f}")
    print(f"Up/Down Accuracy: {up_down_accuracy:.2%}")
    
    return mae, mre, up_down_accuracy

if __name__ == '__main__':
    results = []
    for d in tqdm(range(2, 100)):
        _, _, acc = main(d)
        results.append((d, acc))
    
    results = np.array(results)
    plt.plot(results[:, 0], results[:, 1])
    plt.show()
