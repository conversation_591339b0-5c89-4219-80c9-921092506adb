import json
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from matplotlib import pyplot as plt
from tqdm import tqdm

dataset_base = './data/BTCUSDT_15m_dataset'

def work(fname: str, d: int = 10):
    with open(fname, "r") as f:
        data = json.load(f)

    columns = [
        "open_time", "open", "high", "low", "close", 
        "volume", "close_time", "quote_volume", 
        "trades", "taker_buy_base_volume", 
        "taker_buy_quote_volume", "ignore"
    ]

    df = pd.DataFrame(data, columns=columns)
    for col in ["open", "high", "low", "close", "volume", "quote_volume",
                "taker_buy_base_volume", "taker_buy_quote_volume"]:
        df[col] = df[col].astype(float)

    close_prices = df["close"].values
    n = len(close_prices)
    results = []
    valid_results = []
    for i in range(d, n - 1):
        X = np.arange(d).reshape(-1, 1)
        y = close_prices[i-d:i]
        model = LinearRegression()
        model.fit(X, y)
        r2 = model.score(X, y)
        if r2 > 0.99:
            pred = model.predict(np.array([[d + 1]]))[0]
            actual = close_prices[i+1]
            mae = abs(pred - actual)
            mre = abs(pred - actual) / close_prices[i-1]
            real_change = actual - close_prices[i-1]
            pred_change = pred - close_prices[i-1]
            correct = (real_change * pred_change) > 0
            # if not correct:
            #     y_fit = model.predict(X)
            #     plt.figure()
            #     plt.title(f"Prediction Error at index {i}")
            #     plt.plot(X, y_fit, color='orange', label='Fitted line')
            #     plt.plot(X, y, label='History')
            #     plt.scatter([d], [actual], color='red', label='Actual next')
            #     plt.scatter([d], [pred], color='green', label='Predicted next')
            #     plt.legend()
            #     plt.show()
            results.append((mae, mre, correct))
            valid_results.append((mae, mre, correct))
    valid_results = np.array(valid_results)
    if len(valid_results) > 0:
        mae = valid_results[:,0].mean()
        mre = valid_results[:,1].mean()
        accuracy = valid_results[:,2].mean()
    else:
        mae, mre, accuracy = 0, 0, 0
    total_samples = (n - d - 1)
    predicted_count = len(valid_results)
    predicted_ratio = predicted_count / total_samples if total_samples else 0
    print(f"Predicted: {predicted_count} samples ({predicted_ratio:.2%})")
    return mae, mre, accuracy

def main(num_past_samples: int):
    mae, mre, accuracy = work(f'{dataset_base}/val.json', num_past_samples)
    print(f"Results for d={num_past_samples}")
    print(f"MAE: {mae:.6f}")
    print(f"MRE (relative to t close): {mre:.6f}")
    print(f"Up/Down Accuracy: {accuracy:.2%}")
    return mae, mre, accuracy

if __name__ == '__main__':
    results = []
    for d in tqdm(range(3, 100)):
        _, _, acc = main(d)
        results.append((d, acc))
    results = np.array(results)
    plt.plot(results[:,0], results[:,1])
    plt.show()
