import json
import math
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import accuracy_score

# [
#   [
#     1499040000000,      // 开盘时间 0
#     "0.01634790",       // 开盘价 1
#     "0.80000000",       // 最高价 2
#     "0.01575800",       // 最低价 3
#     "0.01577100",       // 收盘价(当前K线未结束的即为最新价) 4
#     "148976.11427815",  // 成交量 5
#     1499644799999,      // 收盘时间 6
#     "2434.19055334",    // 成交额 7
#     308,                // 成交笔数 8
#     "1756.87402397",    // 主动买入成交量 9
#     "28.46694368",      // 主动买入成交额 10
#     "17928899.62484339" // 请忽略该参数 11
#   ]
# ]

# 超参数配置
config = {
    "d": 3,                  # 时间窗口长度（15分钟*60=15小时）
    "batch_size": 64,
    "d_model": 64,            # Transformer特征维度
    "nhead": 4,               # 注意力头数
    "num_layers": 3,          # Transformer层数
    "dim_feedforward": 256,   # 前馈网络维度
    "dropout": 0.1,
    "lr": 1e-3,
    "num_epochs": 20,
    "device": torch.device("cuda" if torch.cuda.is_available() else "cpu")
}

# 数据预处理
def load_and_process_data(file_path, window_size):
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # data = data[:100] # for debug
    
    samples = []
    labels = []
    # price_feature_indices = [1, 2, 3, 4]  # 价格特征
    price_feature_indices = [4]  # 价格特征
    # other_feature_indices = [5, 7, 8, 9, 10]  # 其他特征
    other_feature_indices = [5]  # 其他特征
    
    for i in range(len(data) - window_size - 2):
        # 提取窗口数据
        window = data[i:i+window_size]
        
        # 提取特征并转换为float
        price_features = []
        other_features = []
        for tp in window:
            price_features.append([float(tp[idx]) for idx in price_feature_indices])
            other_features.append([float(tp[idx]) for idx in other_feature_indices])
        price_features = np.array(price_features)
        other_features = np.array(other_features)
        
        # 样本内归一化
        price_mean = price_features.mean()
        price_std = price_features.std()
        price_std = price_std if price_std != 0 else 1e-8  # 防止除零
        normalized_price = (price_features - price_mean) / price_std
        
        other_mean = other_features.mean(axis=0)
        other_std = other_features.std(axis=0)
        other_std[other_std == 0] = 1e-8  # 防止除零
        normalized_other = (other_features - other_mean) / other_std
        
        # 合并归一化后的特征
        normalized = np.hstack((normalized_price, normalized_other))
        
        # 生成标签
        current_close = float(data[i+window_size-1][4])
        future_close = float(data[i+window_size+1][4])
        # future_close = float(data[i+window_size-2][4])
        label = 1 if future_close > current_close else 0
        # label = 1
        
        samples.append(normalized)
        labels.append(label)
    
    return np.array(samples), np.array(labels)

# 数据集类
class BTCDataset(Dataset):
    def __init__(self, samples, labels):
        self.samples = torch.FloatTensor(samples)
        self.labels = torch.LongTensor(labels)
        
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        return self.samples[idx], self.labels[idx]

# 位置编码
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        # x = x + self.pe[:, :x.size(1)]
        # x = torch.concat((x, torch.repeat_interleave(self.pe[:, :x.size(1)], x.size(0), dim=0)), dim=-1)
        return self.dropout(x)

# Transformer模型
class PricePredictor(nn.Module):
    def __init__(self, config):
        super().__init__()
        self.input_dim = len(config["feature_indices"]) if "feature_indices" in config else 2
        self.d_model = config["d_model"]
        
        self.input_embed = nn.Linear(self.input_dim, self.d_model)
        self.pos_encoder = PositionalEncoding(self.d_model, config["dropout"])
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.d_model,
            nhead=config["nhead"],
            dim_feedforward=config["dim_feedforward"],
            dropout=config["dropout"]
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, config["num_layers"])
        self.classifier = nn.Linear(self.d_model, 2)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_dim)
        x = self.input_embed(x)  # (batch_size, seq_len, d_model)
        x = x.permute(1, 0, 2)   # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        x = self.transformer(x)  # (seq_len, batch_size, d_model)
        x = x.mean(dim=0)        # 平均所有时间步
        return self.classifier(x)

# 训练流程
def train_model():
    # 加载数据
    dataset_base = './data/BTCUSDT_15m_dataset'
    X_train, y_train = load_and_process_data(f'{dataset_base}/train.json', config["d"])
    X_val, y_val = load_and_process_data(f'{dataset_base}/val.json', config["d"])
    
    # 创建DataLoader
    train_loader = DataLoader(BTCDataset(X_train, y_train), 
                             batch_size=config["batch_size"], shuffle=True)
    val_loader = DataLoader(BTCDataset(X_val, y_val), 
                           batch_size=config["batch_size"], shuffle=False)
    
    # 初始化模型
    model = PricePredictor(config).to(config["device"])
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=config["lr"])
    
    # 训练循环
    for epoch in range(config["num_epochs"]):
        model.train()
        total_loss = 0
        
        for inputs, labels in train_loader:
            inputs = inputs.to(config["device"])
            labels = labels.to(config["device"])
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        # 验证
        model.eval()
        val_preds, val_labels = [], []
        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs = inputs.to(config["device"])
                outputs = model(inputs)
                preds = torch.argmax(outputs, dim=1)
                val_preds.extend(preds.cpu().numpy())
                val_labels.extend(labels.cpu().numpy())
        
        # 打印统计信息
        train_loss = total_loss / len(train_loader)
        val_acc = accuracy_score(val_labels, val_preds)
        print(f"Epoch {epoch+1}/{config['num_epochs']}")
        print(f"Train Loss: {train_loss:.4f} | Val Acc: {val_acc:.4f}")
        print("-"*50)
    
    print("训练完成")
    return model

if __name__ == "__main__":
    model = train_model()