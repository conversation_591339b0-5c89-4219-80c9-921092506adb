#!/usr/bin/env python3
"""
演示优化后的NMS处理器
使用合理的参数设置
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from box_nms_processor import BoxNMSProcessor


def demo_with_reasonable_params():
    """使用合理参数进行演示"""
    
    print("K线箱体NMS处理器演示")
    print("=" * 50)
    
    # 使用合理的参数 - 约5000个候选
    processor = BoxNMSProcessor(
        iou_threshold=0.3,
        min_score=85.0  # 根据分析结果，这将产生约5000个候选
    )
    
    print(f"配置参数:")
    print(f"  IoU阈值: {processor.iou_threshold}")
    print(f"  最小得分: {processor.min_score}")
    
    start_time = time.time()
    
    try:
        # 执行处理
        results = processor.process(
            scores_csv_path="scores.csv",
            klines_json_path="BTCUSDT_4h.json",
            output_path="tmp/demo_results.json"
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n{'='*50}")
        print(f"处理完成!")
        print(f"总处理时间: {processing_time:.2f} 秒")
        
        # 显示结果摘要
        metadata = results['metadata']
        print(f"\n结果摘要:")
        print(f"  原始候选数量: {metadata['total_candidates']:,}")
        print(f"  NMS后箱体数量: {metadata['selected_boxes']:,}")
        print(f"  压缩比: {metadata['selected_boxes']/metadata['total_candidates']*100:.1f}%")
        print(f"  K线数据量: {metadata['klines_count']:,}")
        
        # 显示前5个箱体的详细信息
        print(f"\n前5个高分箱体详情:")
        for i, box in enumerate(results['boxes'][:5]):
            boundaries = box['boundaries']
            indicators = box['technical_indicators']
            
            print(f"\n  箱体 {i+1}:")
            print(f"    得分: {box['score']:.1f}")
            print(f"    时间范围: K线 {boundaries['left_idx']} - {boundaries['right_idx']} "
                  f"({boundaries['right_idx'] - boundaries['left_idx'] + 1} 根K线)")
            print(f"    价格范围: {boundaries['min_price']:.1f} - {boundaries['max_price']:.1f} "
                  f"(范围: {boundaries['max_price'] - boundaries['min_price']:.1f})")

            # 显示时间信息
            if 'time_info' in indicators:
                time_info = indicators['time_info']
                print(f"    开始时间: {time_info['start_time']}")
                print(f"    结束时间: {time_info['end_time']}")
                if time_info['duration_ms']:
                    duration_days = time_info['duration_ms'] / (1000 * 60 * 60 * 24)
                    print(f"    实际持续: {duration_days:.1f} 天")
            
            if 'basic_stats' in indicators:
                basic = indicators['basic_stats']
                print(f"    持续时间: {basic['duration_hours']} 小时")
                print(f"    箱体面积: {basic['area']:.0f}")
            
            if 'volume_stats' in indicators:
                volume = indicators['volume_stats']
                print(f"    平均成交量: {volume['avg_volume']:.0f}")
            
            if 'strength_indicators' in indicators:
                strength = indicators['strength_indicators']
                print(f"    边界触碰: 上{strength['upper_touches']}次, 下{strength['lower_touches']}次 "
                      f"(比率: {strength['touch_ratio']:.1%})")
        
        # 统计分析
        print(f"\n箱体统计分析:")
        scores = [box['score'] for box in results['boxes']]
        durations = [box['technical_indicators']['basic_stats']['duration_candles'] 
                    for box in results['boxes']]
        price_ranges = [box['boundaries']['max_price'] - box['boundaries']['min_price'] 
                       for box in results['boxes']]
        
        print(f"  得分分布: {min(scores):.1f} - {max(scores):.1f} (平均: {sum(scores)/len(scores):.1f})")
        print(f"  持续时间: {min(durations)} - {max(durations)} 根K线 (平均: {sum(durations)/len(durations):.1f})")
        print(f"  价格范围: {min(price_ranges):.1f} - {max(price_ranges):.1f} (平均: {sum(price_ranges)/len(price_ranges):.1f})")
        
        print(f"\n结果已保存到: tmp/demo_results.json")
        
        return True
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_different_thresholds():
    """演示不同IoU阈值的效果"""
    
    print(f"\n{'='*60}")
    print("不同IoU阈值效果对比")
    print(f"{'='*60}")
    
    iou_thresholds = [0.2, 0.3, 0.5]
    min_score = 85.0  # 固定最小得分
    
    for iou_threshold in iou_thresholds:
        print(f"\n测试 IoU阈值: {iou_threshold}")
        print("-" * 30)
        
        processor = BoxNMSProcessor(
            iou_threshold=iou_threshold,
            min_score=min_score
        )
        
        start_time = time.time()
        
        try:
            results = processor.process(
                scores_csv_path="scores.csv",
                klines_json_path="BTCUSDT_4h.json",
                output_path=f"tmp/results_iou_{iou_threshold}.json"
            )
            
            processing_time = time.time() - start_time
            
            print(f"  处理时间: {processing_time:.1f}秒")
            print(f"  最终箱体数: {results['metadata']['selected_boxes']}")
            print(f"  压缩比: {results['metadata']['selected_boxes']/results['metadata']['total_candidates']*100:.1f}%")
            
        except Exception as e:
            print(f"  处理失败: {e}")


if __name__ == "__main__":
    # 主演示
    success = demo_with_reasonable_params()
    
    if success:
        # 对比不同IoU阈值
        demo_different_thresholds()
    
    print(f"\n{'='*60}")
    print("演示完成!")
