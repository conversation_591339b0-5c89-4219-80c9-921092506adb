#!/usr/bin/env python3
"""
测试优化后的NMS处理器性能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from box_nms_processor import BoxNMSProcessor


def test_performance():
    """测试不同参数下的性能"""
    
    # 测试不同的最小得分阈值
    test_configs = [
        {"min_score": 30.0, "iou_threshold": 0.3, "name": "严格过滤"},
        {"min_score": 25.0, "iou_threshold": 0.3, "name": "中等过滤"},
        {"min_score": 20.0, "iou_threshold": 0.3, "name": "宽松过滤"},
    ]
    
    for config in test_configs:
        print(f"\n{'='*50}")
        print(f"测试配置: {config['name']}")
        print(f"最小得分: {config['min_score']}, IoU阈值: {config['iou_threshold']}")
        print(f"{'='*50}")
        
        start_time = time.time()
        
        try:
            # 创建处理器
            processor = BoxNMSProcessor(
                iou_threshold=config['iou_threshold'],
                min_score=config['min_score']
            )
            
            # 执行处理
            results = processor.process(
                scores_csv_path="scores.csv",
                klines_json_path="BTCUSDT_4h.json",
                output_path=f"tmp/test_results_{config['min_score']}.json"
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"\n处理完成!")
            print(f"处理时间: {processing_time:.2f} 秒")
            print(f"原始候选数: {results['metadata']['total_candidates']}")
            print(f"最终箱体数: {results['metadata']['selected_boxes']}")
            print(f"压缩比: {results['metadata']['selected_boxes']/results['metadata']['total_candidates']*100:.2f}%")
            
            # 显示前3个箱体的基本信息
            print(f"\n前3个箱体:")
            for i, box in enumerate(results['boxes'][:3]):
                print(f"  箱体{i+1}: 得分={box['score']:.1f}, "
                      f"时间={box['boundaries']['left_idx']}-{box['boundaries']['right_idx']}, "
                      f"价格={box['boundaries']['min_price']:.1f}-{box['boundaries']['max_price']:.1f}")
            
        except Exception as e:
            print(f"处理失败: {e}")
            import traceback
            traceback.print_exc()


def quick_analysis():
    """快速分析scores.csv的分布"""
    import numpy as np
    import csv
    
    print("分析scores.csv的得分分布...")
    
    scores = []
    with open("scores.csv", 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            for cell in row:
                if cell.strip() != '':
                    try:
                        score = float(cell)
                        if score > 0:
                            scores.append(score)
                    except ValueError:
                        pass
    
    if scores:
        scores = np.array(scores)
        print(f"总得分数量: {len(scores)}")
        print(f"得分范围: {scores.min():.1f} - {scores.max():.1f}")
        print(f"平均得分: {scores.mean():.1f}")
        print(f"得分中位数: {np.median(scores):.1f}")
        
        # 分析不同阈值下的候选数量
        thresholds = [10, 15, 20, 25, 30, 35, 40]
        print(f"\n不同阈值下的候选数量:")
        for threshold in thresholds:
            count = np.sum(scores >= threshold)
            print(f"  >= {threshold}: {count} 个候选 ({count/len(scores)*100:.1f}%)")
    else:
        print("未找到有效得分数据")


if __name__ == "__main__":
    print("优化后的NMS处理器性能测试")
    print("=" * 60)
    
    # 首先分析数据分布
    quick_analysis()
    
    # 然后测试性能
    test_performance()
    
    print(f"\n{'='*60}")
    print("测试完成!")
