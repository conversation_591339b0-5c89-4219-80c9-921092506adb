#!/usr/bin/env python3
"""
分析scores.csv的数据分布
"""

import numpy as np
import csv


def analyze_scores():
    """分析scores.csv的得分分布"""
    print("分析scores.csv的得分分布...")
    
    scores = []
    total_cells = 0
    non_empty_cells = 0
    
    with open("scores.csv", 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row_idx, row in enumerate(reader):
            for col_idx, cell in enumerate(row):
                total_cells += 1
                if cell.strip() != '':
                    non_empty_cells += 1
                    try:
                        score = float(cell)
                        if score > 0:
                            scores.append(score)
                    except ValueError:
                        pass
    
    print(f"总单元格数: {total_cells}")
    print(f"非空单元格数: {non_empty_cells}")
    print(f"有效得分数: {len(scores)}")
    
    if scores:
        scores = np.array(scores)
        print(f"\n得分统计:")
        print(f"  范围: {scores.min():.1f} - {scores.max():.1f}")
        print(f"  平均值: {scores.mean():.1f}")
        print(f"  中位数: {np.median(scores):.1f}")
        print(f"  标准差: {scores.std():.1f}")
        
        # 百分位数
        percentiles = [50, 75, 90, 95, 99]
        print(f"\n百分位数:")
        for p in percentiles:
            value = np.percentile(scores, p)
            print(f"  {p}%: {value:.1f}")
        
        # 分析不同阈值下的候选数量
        thresholds = [10, 20, 30, 40, 50, 60, 70, 80, 90]
        print(f"\n不同阈值下的候选数量:")
        for threshold in thresholds:
            count = np.sum(scores >= threshold)
            percentage = count/len(scores)*100
            print(f"  >= {threshold:2d}: {count:8d} 个候选 ({percentage:5.1f}%)")
        
        # 推荐阈值
        print(f"\n推荐阈值设置:")
        target_candidates = [1000, 5000, 10000, 50000]
        for target in target_candidates:
            if target < len(scores):
                threshold = np.percentile(scores, (1 - target/len(scores)) * 100)
                print(f"  约 {target:5d} 个候选: min_score >= {threshold:.1f}")
    else:
        print("未找到有效得分数据")


if __name__ == "__main__":
    analyze_scores()
