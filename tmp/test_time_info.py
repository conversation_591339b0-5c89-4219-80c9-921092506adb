#!/usr/bin/env python3
"""
测试箱体时间信息功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from box_nms_processor import BoxNMSProcessor


def test_time_info():
    """测试时间信息功能"""
    
    print("测试箱体时间信息功能")
    print("=" * 50)
    
    # 使用较高的得分阈值以获得少量高质量箱体
    processor = BoxNMSProcessor(
        iou_threshold=0.3,
        min_score=90.0  # 使用更高阈值获得少量箱体
    )
    
    try:
        # 执行处理
        results = processor.process(
            scores_csv_path="scores.csv",
            klines_json_path="BTCUSDT_4h.json",
            output_path="tmp/time_test_results.json"
        )
        
        print(f"\n处理完成，找到 {len(results['boxes'])} 个箱体")
        
        # 显示每个箱体的详细时间信息
        for i, box in enumerate(results['boxes']):
            print(f"\n{'='*60}")
            print(f"箱体 {i+1} 详细信息:")
            print(f"{'='*60}")
            
            boundaries = box['boundaries']
            indicators = box['technical_indicators']
            
            print(f"得分: {box['score']:.1f}")
            print(f"K线索引范围: {boundaries['left_idx']} - {boundaries['right_idx']}")
            print(f"价格范围: {boundaries['min_price']:.2f} - {boundaries['max_price']:.2f}")
            
            # 时间信息
            if 'time_info' in indicators:
                time_info = indicators['time_info']
                print(f"\n⏰ 时间信息:")
                print(f"  开始时间戳: {time_info['start_timestamp']}")
                print(f"  结束时间戳: {time_info['end_timestamp']}")
                print(f"  开始时间: {time_info['start_time']}")
                print(f"  结束时间: {time_info['end_time']}")
                
                if time_info['duration_ms']:
                    duration_seconds = time_info['duration_ms'] / 1000
                    duration_minutes = duration_seconds / 60
                    duration_hours = duration_minutes / 60
                    duration_days = duration_hours / 24
                    
                    print(f"  持续时间: {time_info['duration_ms']} 毫秒")
                    print(f"  持续时间: {duration_seconds:.0f} 秒")
                    print(f"  持续时间: {duration_minutes:.0f} 分钟")
                    print(f"  持续时间: {duration_hours:.1f} 小时")
                    print(f"  持续时间: {duration_days:.1f} 天")
            
            # 基础统计
            if 'basic_stats' in indicators:
                basic = indicators['basic_stats']
                print(f"\n📊 基础统计:")
                print(f"  K线数量: {basic['duration_candles']} 根")
                print(f"  理论小时数: {basic['duration_hours']} 小时")
                print(f"  价格范围: {basic['price_range']:.2f}")
                print(f"  箱体面积: {basic['area']:.0f}")
            
            # 价格统计
            if 'price_stats' in indicators:
                price = indicators['price_stats']
                print(f"\n💰 价格统计:")
                print(f"  开盘价: {price['open_price']:.2f}")
                print(f"  收盘价: {price['close_price']:.2f}")
                print(f"  价格变化: {price['price_change']:.2f} ({price['price_change_pct']:.2f}%)")
                print(f"  价格波动率: {price['price_volatility']:.2f}")
            
            # 成交量统计
            if 'volume_stats' in indicators:
                volume = indicators['volume_stats']
                print(f"\n📈 成交量统计:")
                print(f"  总成交量: {volume['total_volume']:.0f}")
                print(f"  平均成交量: {volume['avg_volume']:.0f}")
                print(f"  成交量标准差: {volume['volume_std']:.0f}")
                print(f"  最大成交量: {volume['max_volume']:.0f}")
                print(f"  最小成交量: {volume['min_volume']:.0f}")
            
            # 强度指标
            if 'strength_indicators' in indicators:
                strength = indicators['strength_indicators']
                print(f"\n💪 强度指标:")
                print(f"  上边界触碰: {strength['upper_touches']} 次")
                print(f"  下边界触碰: {strength['lower_touches']} 次")
                print(f"  总触碰次数: {strength['upper_touches'] + strength['lower_touches']} 次")
                print(f"  触碰比率: {strength['touch_ratio']:.2%}")
        
        print(f"\n{'='*60}")
        print(f"详细结果已保存到: tmp/time_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_time_info()
