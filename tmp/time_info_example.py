#!/usr/bin/env python3
"""
箱体时间信息功能演示

展示如何获取和使用箱体的时间信息，包括：
- 开始和结束时间戳
- 可读的时间格式
- 持续时间计算
"""

import sys
import os
import json
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from box_nms_processor import BoxNMSProcessor


def format_duration(duration_ms):
    """格式化持续时间为可读格式"""
    if not duration_ms:
        return "未知"
    
    seconds = duration_ms / 1000
    minutes = seconds / 60
    hours = minutes / 60
    days = hours / 24
    
    if days >= 1:
        return f"{days:.1f} 天"
    elif hours >= 1:
        return f"{hours:.1f} 小时"
    elif minutes >= 1:
        return f"{minutes:.0f} 分钟"
    else:
        return f"{seconds:.0f} 秒"


def analyze_time_patterns(boxes):
    """分析箱体的时间模式"""
    print("\n📅 箱体时间模式分析:")
    print("-" * 50)
    
    if not boxes:
        print("没有箱体数据可分析")
        return
    
    # 收集时间数据
    durations = []
    start_times = []
    
    for box in boxes:
        time_info = box['technical_indicators'].get('time_info', {})
        if time_info.get('duration_ms'):
            durations.append(time_info['duration_ms'])
        
        if time_info.get('start_timestamp'):
            start_times.append(time_info['start_timestamp'])
    
    if durations:
        avg_duration = sum(durations) / len(durations)
        min_duration = min(durations)
        max_duration = max(durations)
        
        print(f"持续时间统计:")
        print(f"  平均持续: {format_duration(avg_duration)}")
        print(f"  最短持续: {format_duration(min_duration)}")
        print(f"  最长持续: {format_duration(max_duration)}")
    
    if start_times:
        start_times.sort()
        earliest = datetime.fromtimestamp(start_times[0] / 1000)
        latest = datetime.fromtimestamp(start_times[-1] / 1000)
        
        print(f"\n时间范围:")
        print(f"  最早开始: {earliest.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  最晚开始: {latest.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  时间跨度: {(latest - earliest).days} 天")


def demo_time_info():
    """演示时间信息功能"""
    
    print("🕐 箱体时间信息功能演示")
    print("=" * 60)
    
    # 使用中等阈值获得几个箱体进行演示
    processor = BoxNMSProcessor(
        iou_threshold=0.5,  # 使用较宽松的IoU阈值
        min_score=85.0      # 保持较高的质量阈值
    )
    
    print(f"配置参数:")
    print(f"  IoU阈值: {processor.iou_threshold}")
    print(f"  最小得分: {processor.min_score}")
    
    try:
        # 执行处理
        results = processor.process(
            scores_csv_path="scores.csv",
            klines_json_path="BTCUSDT_4h.json",
            output_path="tmp/time_demo_results.json"
        )
        
        boxes = results['boxes']
        print(f"\n找到 {len(boxes)} 个箱体")
        
        if not boxes:
            print("没有找到符合条件的箱体，请尝试降低min_score阈值")
            return
        
        # 显示每个箱体的时间信息
        for i, box in enumerate(boxes):
            print(f"\n{'='*50}")
            print(f"📦 箱体 {i+1}")
            print(f"{'='*50}")
            
            boundaries = box['boundaries']
            time_info = box['technical_indicators'].get('time_info', {})
            basic_stats = box['technical_indicators'].get('basic_stats', {})
            
            print(f"得分: {box['score']:.1f}")
            print(f"K线索引: {boundaries['left_idx']} → {boundaries['right_idx']}")
            print(f"价格范围: {boundaries['min_price']:.2f} → {boundaries['max_price']:.2f}")
            
            if time_info:
                print(f"\n⏰ 时间详情:")
                print(f"  开始时间: {time_info.get('start_time', '未知')}")
                print(f"  结束时间: {time_info.get('end_time', '未知')}")
                print(f"  持续时间: {format_duration(time_info.get('duration_ms'))}")
                
                # 计算一些有趣的时间统计
                if time_info.get('start_timestamp') and time_info.get('end_timestamp'):
                    start_dt = datetime.fromtimestamp(time_info['start_timestamp'] / 1000)
                    end_dt = datetime.fromtimestamp(time_info['end_timestamp'] / 1000)
                    
                    print(f"  开始星期: {start_dt.strftime('%A')}")
                    print(f"  结束星期: {end_dt.strftime('%A')}")
                    print(f"  跨越月份: {start_dt.strftime('%Y-%m')} 到 {end_dt.strftime('%Y-%m')}")
            
            if basic_stats:
                print(f"\n📊 基础信息:")
                print(f"  K线数量: {basic_stats.get('duration_candles', 0)} 根")
                print(f"  理论时长: {basic_stats.get('duration_hours', 0)} 小时")
                
                # 比较理论时长和实际时长
                if time_info.get('duration_ms'):
                    actual_hours = time_info['duration_ms'] / (1000 * 60 * 60)
                    theoretical_hours = basic_stats.get('duration_hours', 0)
                    if theoretical_hours > 0:
                        ratio = actual_hours / theoretical_hours
                        print(f"  实际时长: {actual_hours:.1f} 小时")
                        print(f"  时长比率: {ratio:.3f} (实际/理论)")
        
        # 分析时间模式
        analyze_time_patterns(boxes)
        
        print(f"\n💾 详细结果已保存到: tmp/time_demo_results.json")
        
        # 提供一些使用建议
        print(f"\n💡 使用建议:")
        print(f"  1. 使用 start_timestamp 和 end_timestamp 进行精确的时间计算")
        print(f"  2. 使用 start_time 和 end_time 进行可读的时间显示")
        print(f"  3. 使用 duration_ms 计算精确的持续时间")
        print(f"  4. 结合价格数据分析特定时间段的市场行为")
        
        return True
        
    except Exception as e:
        print(f"演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_json_structure():
    """展示JSON输出结构中的时间信息部分"""
    
    print(f"\n📋 JSON输出结构 - 时间信息部分:")
    print("-" * 50)
    
    example_structure = {
        "time_info": {
            "start_timestamp": "开始时间戳（毫秒）",
            "end_timestamp": "结束时间戳（毫秒）", 
            "start_time": "开始时间（YYYY-MM-DD HH:MM:SS）",
            "end_time": "结束时间（YYYY-MM-DD HH:MM:SS）",
            "duration_ms": "持续时间（毫秒）"
        }
    }
    
    print(json.dumps(example_structure, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    # 显示JSON结构
    show_json_structure()
    
    # 运行演示
    demo_time_info()
    
    print(f"\n{'='*60}")
    print("时间信息功能演示完成！")
