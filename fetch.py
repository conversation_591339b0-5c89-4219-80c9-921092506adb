import requests
import json
import os
import time
from datetime import datetime, timezone
from loguru import logger
from tqdm import tqdm

# curl --location 'https://fapi.binance.com/fapi/v1/klines?symbol=BTCUSDT&interval=15m&startTime=1704038400000' --header 'Content-Type: application/json'

api_base = 'https://fapi.binance.com/fapi/v1/klines'
limit_num = 1500
interval = '4h'
ts_delta = 240 * 60 * 1000 * limit_num
ts_tail_delta = 240 * 60 * 1000 * (limit_num - 1)

symbol = 'ETHUSDT'
output_dir = 'ETHUSDT_4h'

def date2timestamp(date: datetime) -> int:
    return int(date.timestamp() * 1000)

if __name__ == '__main__':
    start_time = datetime.fromisoformat('2024-08-01T00:00:00Z')
    end_time = datetime.fromisoformat('2025-08-01T00:00:00Z')
    start_ts = date2timestamp(start_time)
    end_ts = date2timestamp(end_time)
    logger.info(f'Start time: {start_time}, End time: {end_time}')

    os.makedirs(output_dir, exist_ok=True)
    for cur_ts in tqdm(range(start_ts, end_ts, ts_delta)):
        if cur_ts >= end_ts:
            logger.success('Finished fetching data')
            break
        fname = f'{output_dir}/{cur_ts}.json'
        if os.path.exists(fname):
            logger.info(f'{fname} exists, skip')
            cur_ts += ts_delta
            continue

        logger.info(f'Fetching {datetime.fromtimestamp(cur_ts / 1000, tz=timezone.utc)}')
        url = f'{api_base}?symbol={symbol}&interval={interval}&startTime={cur_ts}&limit={limit_num}'
        logger.debug(f'URL: {url}')
        resp = requests.get(url)
        resp.raise_for_status()
        data = resp.json()
        head_ts = data[0][0]
        tail_ts = data[-1][0]
        logger.debug(f'Fetched data from {head_ts} to {tail_ts}')
        assert head_ts == cur_ts, f'Expect head {cur_ts}, got {head_ts}'
        # assert tail_ts == cur_ts + ts_tail_delta, f'Expect tail {cur_ts + ts_tail_delta}, got {tail_ts}'
        
        with open(fname, 'w') as f:
            json.dump(data, f, indent=4)
        
        time.sleep(0.5)