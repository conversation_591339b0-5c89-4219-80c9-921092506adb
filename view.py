# [
#   [
#     1499040000000,      // 开盘时间
#     "0.01634790",       // 开盘价
#     "0.80000000",       // 最高价
#     "0.01575800",       // 最低价
#     "0.01577100",       // 收盘价(当前K线未结束的即为最新价)
#     "148976.11427815",  // 成交量
#     1499644799999,      // 收盘时间
#     "2434.19055334",    // 成交额
#     308,                // 成交笔数
#     "1756.87402397",    // 主动买入成交量
#     "28.46694368",      // 主动买入成交额
#     "17928899.62484339" // 请忽略该参数
#   ]
# ]

from matplotlib import pyplot as plt
import json

def visualize_kline(kline_data):
    times = [item[0] for item in kline_data]
    close_prices = [float(item[4]) for item in kline_data]
    plt.plot(times, close_prices, label='Close Price')
    plt.legend()
    plt.show()

if __name__ == '__main__':
    with open('2024_all.json', 'r') as f:
        kline_data = json.load(f)
    visualize_kline(kline_data)